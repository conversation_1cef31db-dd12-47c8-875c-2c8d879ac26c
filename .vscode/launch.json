{"version": "0.2.0", "configurations": [{"name": "Debug Expo", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/@expo/cli/build/bin/cli", "args": ["start"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"]}, {"name": "Debug Expo Web", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/@expo/cli/build/bin/cli", "args": ["start", "--web"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"]}]}