/**
 * Test script để kiểm tra việc sửa lỗi thông báo lặp lại
 * Chạy script này để test các scenario có thể gây ra thông báo lặp lại
 */

// Mock các dependencies
const mockNotificationService = {
  lastFallbackAlertTime: 0,
  fallbackAlertCooldown: 30000,
  userInitiatedFlag: false,
  
  // Test fallback alert debounce
  showFallbackAlert(title, message) {
    const now = Date.now();
    
    if (now - this.lastFallbackAlertTime < this.fallbackAlertCooldown) {
      console.log(`⏭️ NotificationService: Fallback alert skipped due to cooldown (${title})`);
      return false; // Không hiển thị
    }
    
    this.lastFallbackAlertTime = now;
    console.log(`📱 NotificationService: Showed fallback alert: ${title}`);
    return true; // Đã hiển thị
  },
  
  // Test user initiated flag
  isUserInitiatedAction() {
    return this.userInitiatedFlag;
  },
  
  markAsUserInitiated() {
    this.userInitiatedFlag = true;
    setTimeout(() => {
      this.userInitiatedFlag = false;
    }, 5000);
  },
  
  // Mock schedule function
  async scheduleShiftReminders(shift) {
    console.log(`📅 Scheduling reminders for shift: ${shift.name}`);
    
    // Simulate Expo Go environment
    const isExpoGo = true;
    const canSchedule = false;
    
    if (!canSchedule) {
      console.log('📱 Workly: Notifications không khả dụng, bỏ qua lập lịch nhắc nhở ca làm việc');
      
      if (isExpoGo && this.isUserInitiatedAction()) {
        return this.showFallbackAlert(
          'Nhắc nhở ca làm việc',
          'Tính năng nhắc nhở ca làm việc sẽ được kích hoạt khi bạn sử dụng development build.'
        );
      }
      return false;
    }
    
    return true;
  }
};

// Test scenarios
async function runTests() {
  console.log('🧪 Bắt đầu test sửa lỗi thông báo lặp lại...\n');
  
  // Test 1: Fallback alert debounce
  console.log('📋 Test 1: Fallback alert debounce');
  console.log('Gọi showFallbackAlert 3 lần liên tiếp:');
  
  const result1 = mockNotificationService.showFallbackAlert('Test 1', 'Message 1');
  const result2 = mockNotificationService.showFallbackAlert('Test 2', 'Message 2');
  const result3 = mockNotificationService.showFallbackAlert('Test 3', 'Message 3');
  
  console.log(`Kết quả: ${result1 ? 'Hiển thị' : 'Bỏ qua'}, ${result2 ? 'Hiển thị' : 'Bỏ qua'}, ${result3 ? 'Hiển thị' : 'Bỏ qua'}`);
  console.log('✅ Chỉ lần đầu tiên được hiển thị, các lần sau bị debounce\n');
  
  // Test 2: User initiated flag
  console.log('📋 Test 2: User initiated flag');
  console.log('Test scheduling khi KHÔNG có user initiated:');
  
  const shift = { name: 'Ca sáng', id: 'shift1' };
  const result4 = await mockNotificationService.scheduleShiftReminders(shift);
  console.log(`Kết quả: ${result4 ? 'Hiển thị fallback' : 'Không hiển thị fallback'}`);
  
  console.log('\nTest scheduling khi CÓ user initiated:');
  mockNotificationService.markAsUserInitiated();
  const result5 = await mockNotificationService.scheduleShiftReminders(shift);
  console.log(`Kết quả: ${result5 ? 'Hiển thị fallback' : 'Không hiển thị fallback'}`);
  console.log('✅ Chỉ hiển thị fallback khi có user initiated\n');
  
  // Test 3: Multiple rapid calls
  console.log('📋 Test 3: Multiple rapid calls simulation');
  console.log('Gọi scheduleShiftReminders 5 lần liên tiếp với user initiated:');
  
  // Reset cooldown
  mockNotificationService.lastFallbackAlertTime = 0;
  
  for (let i = 0; i < 5; i++) {
    mockNotificationService.markAsUserInitiated();
    const result = await mockNotificationService.scheduleShiftReminders({
      name: `Ca ${i + 1}`,
      id: `shift${i + 1}`
    });
    console.log(`Lần ${i + 1}: ${result ? 'Hiển thị fallback' : 'Không hiển thị fallback'}`);
    
    // Simulate small delay between calls
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  console.log('✅ Chỉ lần đầu tiên hiển thị fallback, các lần sau bị debounce\n');
  
  // Test 4: Cooldown expiry
  console.log('📋 Test 4: Cooldown expiry test');
  console.log('Đợi cooldown hết hạn và test lại...');
  
  // Simulate cooldown expiry
  mockNotificationService.lastFallbackAlertTime = Date.now() - 31000; // 31 giây trước
  mockNotificationService.markAsUserInitiated();
  
  const result6 = await mockNotificationService.scheduleShiftReminders({
    name: 'Ca sau cooldown',
    id: 'shift_after_cooldown'
  });
  console.log(`Kết quả sau khi cooldown hết hạn: ${result6 ? 'Hiển thị fallback' : 'Không hiển thị fallback'}`);
  console.log('✅ Fallback được hiển thị lại sau khi cooldown hết hạn\n');
  
  console.log('🎉 Tất cả tests đã hoàn thành!');
  console.log('📊 Tóm tắt sửa lỗi:');
  console.log('   - ✅ Thêm debounce cho fallback alerts (30 giây)');
  console.log('   - ✅ Chỉ hiển thị fallback khi user thực sự tương tác');
  console.log('   - ✅ Hủy thông báo cũ trước khi lập lịch mới');
  console.log('   - ✅ Thêm delay để đảm bảo việc hủy hoàn tất');
  console.log('   - ✅ Debounce cho AlarmService scheduling');
}

// Chạy tests
runTests().catch(console.error);
